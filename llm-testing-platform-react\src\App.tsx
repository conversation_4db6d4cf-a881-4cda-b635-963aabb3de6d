import React, { useState, useEffect } from 'react';
import type { Question, Model, TestResult, ModelScore } from './types';
import { getQuestions, getModels, getTestResults, saveTestResults } from './utils/storage';
import QuestionManager from './components/QuestionManager';
import ModelManager from './components/ModelManager';
import TestingGrid from './components/TestingGrid';
import Leaderboard from './components/Leaderboard';
import CategoryManager from './components/CategoryManager';

function App() {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [activeTab, setActiveTab] = useState<'testing' | 'questions' | 'models' | 'categories' | 'leaderboard'>('testing');

  useEffect(() => {
    setQuestions(getQuestions());
    setModels(getModels());
    setTestResults(getTestResults());
  }, []);

  const updateTestResult = (questionId: string, modelId: string, status: 'passed' | 'failed' | 'pending') => {
    const newResults = testResults.filter(r => !(r.questionId === questionId && r.modelId === modelId));
    newResults.push({ questionId, modelId, status });
    setTestResults(newResults);
    saveTestResults(newResults);
  };

  const calculateLeaderboard = (): ModelScore[] => {
    return models.map(model => {
      const modelResults = testResults.filter(r => r.modelId === model.id);
      const passed = modelResults.filter(r => r.status === 'passed').length;
      const failed = modelResults.filter(r => r.status === 'failed').length;
      const pending = questions.length - passed - failed;
      
      return {
        modelId: model.id,
        modelName: model.name,
        provider: model.provider,
        icon: model.icon,
        imageUrl: model.imageUrl,
        totalQuestions: questions.length,
        passedQuestions: passed,
        failedQuestions: failed,
        pendingQuestions: pending,
        passRate: questions.length > 0 ? (passed / questions.length) * 100 : 0
      };
    }).sort((a, b) => b.passRate - a.passRate);
  };

  return (
    <div className="min-h-screen">
      {/* Animated Header */}
      <div className="glass-effect text-white p-6 shadow-2xl">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold mb-6 gradient-text float-animation">
            🚀 AI Test v2 🤖
          </h1>
          <nav className="flex flex-wrap gap-3">
            <button
              onClick={() => setActiveTab('testing')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'testing'
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600'
              }`}
            >
              🧪 Testing Grid
            </button>
            <button
              onClick={() => setActiveTab('questions')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'questions'
                  ? 'bg-gradient-to-r from-green-600 to-teal-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600'
              }`}
            >
              ❓ Questions
            </button>
            <button
              onClick={() => setActiveTab('models')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'models'
                  ? 'bg-gradient-to-r from-orange-600 to-red-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600'
              }`}
            >
              🤖 Models
            </button>
            <button
              onClick={() => setActiveTab('categories')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'categories'
                  ? 'bg-gradient-to-r from-pink-600 to-purple-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600'
              }`}
            >
              🏷️ Categories
            </button>
            <button
              onClick={() => setActiveTab('leaderboard')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'leaderboard'
                  ? 'bg-gradient-to-r from-yellow-600 to-orange-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600'
              }`}
            >
              🏆 Leaderboard
            </button>
          </nav>
        </div>
      </div>

      {/* Main Content with Animation */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="card-animated">
          {activeTab === 'testing' && (
            <TestingGrid
              questions={questions}
              models={models}
              testResults={testResults}
              onUpdateResult={updateTestResult}
            />
          )}
          {activeTab === 'questions' && (
            <QuestionManager
              questions={questions}
              onQuestionsChange={setQuestions}
            />
          )}
          {activeTab === 'models' && (
            <ModelManager
              models={models}
              onModelsChange={setModels}
            />
          )}
          {activeTab === 'categories' && (
            <CategoryManager />
          )}
          {activeTab === 'leaderboard' && (
            <Leaderboard scores={calculateLeaderboard()} />
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
